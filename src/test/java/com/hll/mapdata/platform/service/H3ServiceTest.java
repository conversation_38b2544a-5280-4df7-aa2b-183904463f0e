package com.hll.mapdata.platform.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.io.WKTReader;

import java.io.IOException;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class H3ServiceTest {

    private H3Service h3Service;
    private GeometryFactory geometryFactory;
    private WKTReader wktReader;

    @BeforeEach
    void setUp() throws IOException {
        h3Service = new H3Service();
        geometryFactory = new GeometryFactory();
        wktReader = new WKTReader();
    }

    @Test
    void testGeometryToH3List_SimplePolygon() throws Exception {
        // 创建一个简单的多边形
        String wkt = "POLYGON((112.0 22.0, 112.1 22.0, 112.1 22.1, 112.0 22.1, 112.0 22.0))";
        Polygon polygon = (Polygon) wktReader.read(wkt);

        List<String> h3Ids = h3Service.geometryToH3List(polygon, 7);

        assertNotNull(h3Ids);
        assertFalse(h3Ids.isEmpty());
        System.out.println("Simple polygon H3 IDs count: " + h3Ids.size());
        System.out.println("H3 IDs: " + h3Ids);
    }

    @Test
    void testGeometryToH3List_PolygonWithHole() throws Exception {
        // 创建一个带孔洞的多边形
        String wkt = "POLYGON((112.0 22.0, 112.2 22.0, 112.2 22.2, 112.0 22.2, 112.0 22.0), " +
                     "(112.05 22.05, 112.15 22.05, 112.15 22.15, 112.05 22.15, 112.05 22.05))";
        Polygon polygonWithHole = (Polygon) wktReader.read(wkt);

        List<String> h3IdsWithHole = h3Service.geometryToH3List(polygonWithHole, 7);

        // 创建相同的多边形但没有孔洞
        String wktNoHole = "POLYGON((112.0 22.0, 112.2 22.0, 112.2 22.2, 112.0 22.2, 112.0 22.0))";
        Polygon polygonNoHole = (Polygon) wktReader.read(wktNoHole);
        List<String> h3IdsNoHole = h3Service.geometryToH3List(polygonNoHole, 7);

        assertNotNull(h3IdsWithHole);
        assertNotNull(h3IdsNoHole);
        
        // 带孔洞的多边形应该有更少的H3单元
        assertTrue(h3IdsWithHole.size() < h3IdsNoHole.size(), 
            "Polygon with hole should have fewer H3 cells than polygon without hole");
        
        System.out.println("Polygon with hole H3 IDs count: " + h3IdsWithHole.size());
        System.out.println("Polygon without hole H3 IDs count: " + h3IdsNoHole.size());
    }

    @Test
    void testGeometryToH3List_MultiPolygon() throws Exception {
        // 创建一个多多边形
        String wkt = "MULTIPOLYGON(((112.0 22.0, 112.1 22.0, 112.1 22.1, 112.0 22.1, 112.0 22.0)), " +
                     "((112.2 22.2, 112.3 22.2, 112.3 22.3, 112.2 22.3, 112.2 22.2)))";
        MultiPolygon multiPolygon = (MultiPolygon) wktReader.read(wkt);

        List<String> h3Ids = h3Service.geometryToH3List(multiPolygon, 7);

        assertNotNull(h3Ids);
        assertFalse(h3Ids.isEmpty());
        System.out.println("MultiPolygon H3 IDs count: " + h3Ids.size());
    }

    @Test
    void testGeometryToH3List_InvalidInputs() {
        // 测试空几何体
        assertThrows(IllegalArgumentException.class, () -> {
            h3Service.geometryToH3List(null, 7);
        });

        // 测试无效分辨率
        Polygon polygon = geometryFactory.createPolygon(new Coordinate[]{
            new Coordinate(112.0, 22.0),
            new Coordinate(112.1, 22.0),
            new Coordinate(112.1, 22.1),
            new Coordinate(112.0, 22.1),
            new Coordinate(112.0, 22.0)
        });

        assertThrows(IllegalArgumentException.class, () -> {
            h3Service.geometryToH3List(polygon, -1);
        });

        assertThrows(IllegalArgumentException.class, () -> {
            h3Service.geometryToH3List(polygon, 16);
        });
    }

    @Test
    void testGeometryToH3List_UnsupportedGeometryType() {
        // 测试不支持的几何体类型
        Point point = geometryFactory.createPoint(new Coordinate(112.0, 22.0));

        assertThrows(IllegalArgumentException.class, () -> {
            h3Service.geometryToH3List(point, 7);
        });
    }

    @Test
    void testGeometryToH3List_DifferentResolutions() throws Exception {
        String wkt = "POLYGON((112.0 22.0, 112.1 22.0, 112.1 22.1, 112.0 22.1, 112.0 22.0))";
        Polygon polygon = (Polygon) wktReader.read(wkt);

        List<String> h3IdsRes5 = h3Service.geometryToH3List(polygon, 5);
        List<String> h3IdsRes7 = h3Service.geometryToH3List(polygon, 7);
        List<String> h3IdsRes9 = h3Service.geometryToH3List(polygon, 9);

        // 更高的分辨率应该产生更多的H3单元
        assertTrue(h3IdsRes5.size() < h3IdsRes7.size());
        assertTrue(h3IdsRes7.size() < h3IdsRes9.size());

        System.out.println("Resolution 5 H3 IDs count: " + h3IdsRes5.size());
        System.out.println("Resolution 7 H3 IDs count: " + h3IdsRes7.size());
        System.out.println("Resolution 9 H3 IDs count: " + h3IdsRes9.size());
    }
}
