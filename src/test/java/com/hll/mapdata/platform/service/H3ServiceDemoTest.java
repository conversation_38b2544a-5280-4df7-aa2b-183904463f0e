package com.hll.mapdata.platform.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.io.WKTReader;

import java.io.IOException;
import java.util.List;

/**
 * 演示改进后的 geometryToH3List 方法的功能
 */
class H3ServiceDemoTest {

    private H3Service h3Service;
    private WKTReader wktReader;

    @BeforeEach
    void setUp() throws IOException {
        h3Service = new H3Service();
        wktReader = new WKTReader();
    }

    @Test
    void demonstratePolygonWithHoleSupport() throws Exception {
        System.out.println("=== 演示多边形孔洞支持 ===");
        
        // 创建一个带孔洞的多边形（外环是一个大矩形，内环是一个小矩形孔洞）
        String polygonWithHoleWkt = "POLYGON((" +
            "112.0 22.0, 112.3 22.0, 112.3 22.3, 112.0 22.3, 112.0 22.0), " +
            "(112.1 22.1, 112.2 22.1, 112.2 22.2, 112.1 22.2, 112.1 22.1))";
        
        Polygon polygonWithHole = (Polygon) wktReader.read(polygonWithHoleWkt);
        List<String> h3IdsWithHole = h3Service.geometryToH3List(polygonWithHole, 8);
        
        // 创建相同的外环但没有孔洞
        String polygonNoHoleWkt = "POLYGON((112.0 22.0, 112.3 22.0, 112.3 22.3, 112.0 22.3, 112.0 22.0))";
        Polygon polygonNoHole = (Polygon) wktReader.read(polygonNoHoleWkt);
        List<String> h3IdsNoHole = h3Service.geometryToH3List(polygonNoHole, 8);
        
        System.out.println("没有孔洞的多边形 H3 单元数量: " + h3IdsNoHole.size());
        System.out.println("有孔洞的多边形 H3 单元数量: " + h3IdsWithHole.size());
        System.out.println("孔洞排除的 H3 单元数量: " + (h3IdsNoHole.size() - h3IdsWithHole.size()));
        
        // 验证带孔洞的多边形确实有更少的H3单元
        assert h3IdsWithHole.size() < h3IdsNoHole.size() : "带孔洞的多边形应该有更少的H3单元";
    }

    @Test
    void demonstrateMultiPolygonSupport() throws Exception {
        System.out.println("\n=== 演示多多边形支持 ===");
        
        // 创建一个多多边形（两个分离的多边形）
        String multiPolygonWkt = "MULTIPOLYGON(" +
            "((112.0 22.0, 112.1 22.0, 112.1 22.1, 112.0 22.1, 112.0 22.0)), " +
            "((112.3 22.3, 112.4 22.3, 112.4 22.4, 112.3 22.4, 112.3 22.3)))";
        
        MultiPolygon multiPolygon = (MultiPolygon) wktReader.read(multiPolygonWkt);
        List<String> h3IdsMulti = h3Service.geometryToH3List(multiPolygon, 8);
        
        // 分别计算每个多边形的H3单元
        Polygon poly1 = (Polygon) multiPolygon.getGeometryN(0);
        Polygon poly2 = (Polygon) multiPolygon.getGeometryN(1);
        
        List<String> h3IdsPoly1 = h3Service.geometryToH3List(poly1, 8);
        List<String> h3IdsPoly2 = h3Service.geometryToH3List(poly2, 8);
        
        System.out.println("多多边形总 H3 单元数量: " + h3IdsMulti.size());
        System.out.println("第一个多边形 H3 单元数量: " + h3IdsPoly1.size());
        System.out.println("第二个多边形 H3 单元数量: " + h3IdsPoly2.size());
        System.out.println("单独计算的总和: " + (h3IdsPoly1.size() + h3IdsPoly2.size()));
        
        // 验证多多边形的H3单元数量等于各个多边形的总和
        assert h3IdsMulti.size() == (h3IdsPoly1.size() + h3IdsPoly2.size()) : 
            "多多边形的H3单元数量应该等于各个多边形的总和";
    }

    @Test
    void demonstrateResolutionScaling() throws Exception {
        System.out.println("\n=== 演示分辨率缩放 ===");
        
        String polygonWkt = "POLYGON((112.0 22.0, 112.2 22.0, 112.2 22.2, 112.0 22.2, 112.0 22.0))";
        Polygon polygon = (Polygon) wktReader.read(polygonWkt);
        
        for (int resolution = 5; resolution <= 10; resolution++) {
            List<String> h3Ids = h3Service.geometryToH3List(polygon, resolution);
            System.out.println("分辨率 " + resolution + ": " + h3Ids.size() + " 个 H3 单元");
        }
    }

    @Test
    void demonstrateInputValidation() throws Exception {
        System.out.println("\n=== 演示输入验证 ===");
        
        String polygonWkt = "POLYGON((112.0 22.0, 112.1 22.0, 112.1 22.1, 112.0 22.1, 112.0 22.0))";
        Polygon polygon = (Polygon) wktReader.read(polygonWkt);
        
        // 测试无效分辨率
        try {
            h3Service.geometryToH3List(polygon, -1);
            assert false : "应该抛出异常";
        } catch (IllegalArgumentException e) {
            System.out.println("✓ 正确捕获无效分辨率异常: " + e.getMessage());
        }
        
        try {
            h3Service.geometryToH3List(polygon, 16);
            assert false : "应该抛出异常";
        } catch (IllegalArgumentException e) {
            System.out.println("✓ 正确捕获无效分辨率异常: " + e.getMessage());
        }
        
        // 测试空几何体
        try {
            h3Service.geometryToH3List(null, 7);
            assert false : "应该抛出异常";
        } catch (IllegalArgumentException e) {
            System.out.println("✓ 正确捕获空几何体异常: " + e.getMessage());
        }
        
        // 测试不支持的几何体类型
        Point point = new GeometryFactory().createPoint(new Coordinate(112.0, 22.0));
        try {
            h3Service.geometryToH3List(point, 7);
            assert false : "应该抛出异常";
        } catch (IllegalArgumentException e) {
            System.out.println("✓ 正确捕获不支持几何体类型异常: " + e.getMessage());
        }
    }
}
