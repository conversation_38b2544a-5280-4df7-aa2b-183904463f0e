package com.hll.mapdata.platform.controller;

import com.hll.map.core.convention.HllResponse;
import com.hll.mapdata.platform.common.BaseController;
import com.hll.mapdata.platform.service.H3Service;
import jakarta.annotation.Resource;
import org.locationtech.jts.geom.Geometry;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/common")
@SuppressWarnings("unchecked")
public class ApiController extends BaseController {

    @Resource
    private H3Service h3Service;

    @GetMapping("/tile-id")
    public HllResponse<String> tile(@RequestParam(required = false, defaultValue = "7") Integer resolution,
                                    @RequestParam Geometry geometry) throws Exception {
        return HllResponse.ok(h3Service.geometryToH3(geometry, resolution));

    }

    @GetMapping("/tile-wkt")
    public HllResponse<String> tileWkt(@RequestParam(value = "tileId") String tileId) throws Exception {
        return HllResponse.ok(h3Service.h3ToGeometry(tileId));

    }


    @GetMapping("/tile-count")
    public HllResponse<List<String>> tileCount(@RequestParam(required = false, defaultValue = "7") Integer resolution,
                                               @RequestParam Geometry geometry) throws Exception {
        return HllResponse.ok(h3Service.geometryToH3List(geometry, resolution));

    }
}
