package com.hll.mapdata.platform.service;

import com.uber.h3core.H3Core;
import com.uber.h3core.util.LatLng;
import org.locationtech.jts.geom.*;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
public class H3Service {

    private final static GeometryFactory geometryFactory = new GeometryFactory();
    private final H3Core h3Core;

    public H3Service() throws IOException {
        this.h3Core = H3Core.newInstance();
    }

    /**
     * 根据传入的几何（WKT）求质心，再根据分辨率计算 H3 索引
     *
     * @param geometry   例如一个 Polygon 的 WKT
     * @param resolution 分辨率 (0 ~ 15), 例如 7
     * @return 返回 H3 hex string 索引，比如 "87411114affffff"
     */
    public String geometryToH3(Geometry geometry, int resolution) throws Exception {
        // 2. 获取质心坐标
        Coordinate centroidCoordinate = geometry.getCentroid().getCoordinate();
        double lon = centroidCoordinate.x; // 经度
        double lat = centroidCoordinate.y; // 纬度

        // 3. 计算 H3 索引
        return h3Core.latLngToCellAddress(lat, lon, resolution);

    }

    /**
     * 将单个 H3 索引转换为对应多边形的 WKT
     *
     * @param h3Index 例如 "87411114affffff"
     * @return 例如 "POLYGON((lon1 lat1, ..., lonN latN, lon1 lat1))"
     */
    public Geometry h3ToGeometry(String h3Index) throws IOException {
        // 1. 获取此 H3 单元对应的多边形边界（经纬度坐标）
        List<LatLng> boundary = h3Core.cellToBoundary(h3Index);
        // 2. 组装成 POLYGON
        Coordinate[] coordinates = new Coordinate[boundary.size() + 1];
        LatLng latLng;
        for (int i = 0; i < boundary.size(); i++) {
            latLng = boundary.get(i);
            coordinates[i] = new Coordinate(latLng.lng, latLng.lat);
        }

        // 闭合多边形：补回首个点坐标
        LatLng first = boundary.get(0);
        coordinates[boundary.size()] = new Coordinate(first.lng, first.lat);
        return geometryFactory.createPolygon(coordinates);
    }

    /**
     * 根据传入的几何体计算覆盖该区域的所有 H3 索引
     *
     * @param geometry   输入的几何体（支持 Polygon 和 MultiPolygon）
     * @param resolution 分辨率 (0 ~ 15)
     * @return 返回覆盖该几何体的所有 H3 索引列表
     * @throws IllegalArgumentException 当输入参数无效时
     * @throws IOException 当 H3 计算出错时
     */
    public List<String> geometryToH3List(Geometry geometry, int resolution) throws IOException {
        // 输入验证
        validateInputs(geometry, resolution);

        List<String> allH3Ids = new ArrayList<>();

        if (geometry instanceof Polygon) {
            allH3Ids.addAll(polygonToH3List((Polygon) geometry, resolution));
        } else if (geometry instanceof MultiPolygon) {
            MultiPolygon multiPolygon = (MultiPolygon) geometry;
            for (int i = 0; i < multiPolygon.getNumGeometries(); i++) {
                Polygon polygon = (Polygon) multiPolygon.getGeometryN(i);
                allH3Ids.addAll(polygonToH3List(polygon, resolution));
            }
        } else {
            throw new IllegalArgumentException("Unsupported geometry type: " + geometry.getGeometryType() +
                ". Only Polygon and MultiPolygon are supported.");
        }

        return allH3Ids;
    }

    /**
     * 验证输入参数
     */
    private void validateInputs(Geometry geometry, int resolution) {
        if (geometry == null) {
            throw new IllegalArgumentException("Geometry cannot be null");
        }
        if (geometry.isEmpty()) {
            throw new IllegalArgumentException("Geometry cannot be empty");
        }
        if (resolution < 0 || resolution > 15) {
            throw new IllegalArgumentException("Resolution must be between 0 and 15, got: " + resolution);
        }
    }

    /**
     * 处理单个多边形，包括外环和内环（孔洞）
     */
    private List<String> polygonToH3List(Polygon polygon, int resolution) throws IOException {
        // 获取外环
        LinearRing exteriorRing = polygon.getExteriorRing();
        List<LatLng> outer = coordinatesToLatLngList(exteriorRing.getCoordinates());

        // 获取内环（孔洞）
        List<List<LatLng>> holes = new ArrayList<>();
        for (int i = 0; i < polygon.getNumInteriorRing(); i++) {
            LinearRing interiorRing = polygon.getInteriorRingN(i);
            List<LatLng> hole = coordinatesToLatLngList(interiorRing.getCoordinates());
            holes.add(hole);
        }

        // 使用 H3 4.x API
        return h3Core.polygonToCellAddresses(outer, holes, resolution);
    }

    /**
     * 将 JTS Coordinate 数组转换为 H3 LatLng 列表
     */
    private List<LatLng> coordinatesToLatLngList(Coordinate[] coordinates) {
        List<LatLng> latLngs = new ArrayList<>(coordinates.length);
        for (Coordinate coord : coordinates) {
            // 注意：LatLng 构造函数是 (lat, lon)，而 Coordinate 是 (x=lon, y=lat)
            latLngs.add(new LatLng(coord.getY(), coord.getX()));
        }
        return latLngs;
    }

    // 简单测试
    public static void main(String[] args) throws Exception {
        Coordinate[] coordinates = new Coordinate[2];
        coordinates[0] = new Coordinate(112.237509, 22.784975);
        coordinates[1] = new Coordinate(112.237045, 22.785753);
        LineString lineString = geometryFactory.createLineString(coordinates);
        H3Service h3Service = new H3Service();
        // 计算7级 H3
        String h3Index = h3Service.geometryToH3(lineString, 7);
        System.out.println("H3索引 = " + h3Index);
        // 计算对应的geometry
        Geometry geometry = h3Service.h3ToGeometry(h3Index);
        System.out.println("H3索引[" + h3Index + "]范围 = " + geometry);
    }
}

    